[{"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Src/Interrupt.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Src/Task_App.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Src/Key_Led.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Src/MPU6050.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Src/Motor.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Src/OLED.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Src/OLED_Font.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Src/PID.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Src/PID_IQMath.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Src/Serial.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Src/SysTick.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Src/Task.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Src/Tracker.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP/inv_mpu.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP/inv_mpu_dmp_motion_driver.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug/ti_msp_dl_config.c"}, {"directory": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/APP/Inc\" -I\"C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/TI_CAR(2)/TI_CAR/main.c"}]