******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 22:27:01 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003ae1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000045e8  0001ba18  R  X
  SRAM                  20200000   00008000  000006a8  00007958  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000045e8   000045e8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004380   00004380    r-x .text
  00004440    00004440    00000120   00000120    r-- .rodata
  00004560    00004560    00000088   00000088    r-- .cinit
20200000    20200000    000004a9   00000000    rw-
  20200000    20200000    00000323   00000000    rw- .bss
  20200324    20200324    00000185   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004380     
                  000000c0    00000364     libc.a : e_asin.c.obj (.text.asin)
                  00000424    000002f8            : s_atan.c.obj (.text.atan)
                  0000071c    000002b0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000009cc    0000022c     MPU6050.o (.text.Read_Quad)
                  00000bf8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00000e24    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001018    000001b0     Task.o (.text.Task_Start)
                  000011c8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000135a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000135c    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000014e4    00000170            : e_sqrt.c.obj (.text.sqrt)
                  00001654    0000016c     Interrupt.o (.text.GROUP1_IRQHandler)
                  000017c0    0000015c     Tracker.o (.text.Tracker_Read)
                  0000191c    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00001a50    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00001b84    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00001ca8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001db4    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00001ebc    00000104     Task_App.o (.text.Task_Motor_PID)
                  00001fc0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000020a4    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002180    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002258    000000cc     Motor.o (.text.Motor_Start)
                  00002324    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  000023e8    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  000024ac    000000b8     Motor.o (.text.Motor_SetDirc)
                  00002564    000000b8     Motor.o (.text.Motor_SetDuty)
                  0000261c    000000b4     Task.o (.text.Task_Add)
                  000026d0    000000a4     Motor.o (.text.Motor_GetSpeed)
                  00002774    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002814    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000028a0    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  0000292c    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  000029b0    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002a34    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00002ab6    00000002     --HOLE-- [fill = 0]
                  00002ab8    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  00002b38    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00002bb8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002c34    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002ca8    00000008     Interrupt.o (.text.SysTick_Handler)
                  00002cb0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002d24    00000068                            : comparedf2.c.obj (.text.__ledf2)
                  00002d8c    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002df0    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002e54    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00002eb8    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002f1a    00000002     --HOLE-- [fill = 0]
                  00002f1c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002f7e    00000002     --HOLE-- [fill = 0]
                  00002f80    00000060     Task_App.o (.text.Task_IdleFunction)
                  00002fe0    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000303e    00000002     --HOLE-- [fill = 0]
                  00003040    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000309c    0000005c     Task_App.o (.text.Task_Tracker)
                  000030f8    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00003150    00000058     Serial.o (.text.Serial_Init)
                  000031a8    00000054     Interrupt.o (.text.Interrupt_Init)
                  000031fc    00000054     MPU6050.o (.text.mspm0_i2c_enable)
                  00003250    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  000032a0    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000032f0    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  0000333c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00003388    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000033d0    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00003414    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00003458    00000044     Task_App.o (.text.Task_Init)
                  0000349c    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  000034e0    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00003522    00000002     --HOLE-- [fill = 0]
                  00003524    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003564    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000035a4    0000003e     Task.o (.text.Task_CMP)
                  000035e2    00000002     --HOLE-- [fill = 0]
                  000035e4    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003620    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000365c    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00003698    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000036d4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003710    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000374c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003786    00000002     --HOLE-- [fill = 0]
                  00003788    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000037c2    00000002     --HOLE-- [fill = 0]
                  000037c4    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000037fc    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003830    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003864    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00003894    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  000038c4    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  000038f4    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003920    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  0000394c    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00003976    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  0000399e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000039c6    00000002     --HOLE-- [fill = 0]
                  000039c8    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  000039f0    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00003a18    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003a40    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003a68    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00003a90    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00003ab8    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003ae0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003b08    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00003b2e    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00003b54    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003b7a    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003ba0    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00003bc4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00003be4    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003c04    00000020     SysTick.o (.text.Delay)
                  00003c24    00000020     main.o (.text.main)
                  00003c44    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003c62    00000002     --HOLE-- [fill = 0]
                  00003c64    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00003c80    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00003c9c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003cb8    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00003cd4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00003cf0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003d0c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003d28    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00003d44    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00003d60    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00003d7c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003d98    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003db4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003dd0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003dec    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00003e08    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00003e20    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00003e38    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003e50    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003e68    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003e80    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00003e98    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003eb0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00003ec8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003ee0    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00003ef8    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00003f10    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00003f28    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00003f40    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00003f58    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00003f70    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00003f88    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00003fa0    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00003fb8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00003fd0    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00003fe8    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00004000    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004018    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004030    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00004048    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00004060    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00004078    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004090    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000040a8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000040c0    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000040d8    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  000040f0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00004108    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00004120    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00004138    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00004150    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00004168    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00004180    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00004198    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  000041b0    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000041c6    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  000041dc    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  000041f2    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00004208    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000421e    00000016     SysTick.o (.text.SysGetTick)
                  00004234    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000424a    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  0000425e    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00004272    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00004286    00000002     --HOLE-- [fill = 0]
                  00004288    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  0000429c    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  000042b0    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000042c4    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000042d8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000042ec    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004300    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00004312    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00004324    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00004336    00000002     --HOLE-- [fill = 0]
                  00004338    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00004348    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004358    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00004368    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00004374    0000000c     SysTick.o (.text.Sys_GetTick)
                  00004380    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000438a    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004394    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  000043a4    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  000043ae    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  000043b8    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  000043c2    00000002     --HOLE-- [fill = 0]
                  000043c4    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  000043d4    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  000043dc    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000043e4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000043ec    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  000043f2    00000002     --HOLE-- [fill = 0]
                  000043f4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00004404    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  0000440a    00000006            : exit.c.obj (.text:abort)
                  00004410    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004414    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004418    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  0000441c    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004420    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00004430    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00004434    0000000c     --HOLE-- [fill = 0]

.cinit     0    00004560    00000088     
                  00004560    00000062     (.cinit..data.load) [load image, compression = lzss]
                  000045c2    00000002     --HOLE-- [fill = 0]
                  000045c4    0000000c     (__TI_handler_table)
                  000045d0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000045d8    00000010     (__TI_cinit_table)

.rodata    0    00004440    00000120     
                  00004440    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00004480    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000044a8    00000028     inv_mpu.o (.rodata.test)
                  000044d0    0000001e     inv_mpu.o (.rodata.reg)
                  000044ee    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000044f0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00004508    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00004520    0000000c     inv_mpu.o (.rodata.hw)
                  0000452c    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00004536    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004538    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  00004540    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00004548    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00004550    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00004556    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  00004559    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  0000455c    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  0000455e    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000323     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    00000010     (.common:quat)
                  20200300    00000006     (.common:Data_Accel)
                  20200306    00000006     (.common:Data_Gyro)
                  2020030c    00000004     (.common:Data_Pitch)
                  20200310    00000004     (.common:Data_Roll)
                  20200314    00000004     (.common:Data_Yaw)
                  20200318    00000004     (.common:ExISR_Flag)
                  2020031c    00000004     (.common:sensor_timestamp)
                  20200320    00000002     (.common:sensors)
                  20200322    00000001     (.common:more)

.data      0    20200324    00000185     UNINITIALIZED
                  20200324    00000044     Motor.o (.data.Motor_Back_Left)
                  20200368    00000044     Motor.o (.data.Motor_Back_Right)
                  202003ac    00000044     Motor.o (.data.Motor_Font_Left)
                  202003f0    00000044     Motor.o (.data.Motor_Font_Right)
                  20200434    0000002c     inv_mpu.o (.data.st)
                  20200460    00000010     Task_App.o (.data.Motor)
                  20200470    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200480    00000008     Task_App.o (.data.Data_MotorEncoder)
                  20200488    00000008     Task_App.o (.data.Data_Tracker_Input)
                  20200490    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  20200494    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  20200498    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  2020049c    00000004     SysTick.o (.data.delayTick)
                  202004a0    00000004     SysTick.o (.data.uwTick)
                  202004a4    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004a6    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004a7    00000001     Task.o (.data.Task_Num)
                  202004a8    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3394    126       0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3434    318       0      
                                                               
    .\APP\Src\
       Interrupt.o                    626     0         6      
       Task_App.o                     516     14        42     
    +--+------------------------------+-------+---------+---------+
       Total:                         1142    14        48     
                                                               
    .\BSP\Src\
       MPU6050.o                      1872    0         47     
       Motor.o                        804     0         272    
       Task.o                         674     0         241    
       Serial.o                       292     0         512    
       PID_IQMath.o                   402     0         0      
       Tracker.o                      370     0         0      
       SysTick.o                      106     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         4520    0         1080   
                                                               
    .\DMP\
       inv_mpu.o                      820     82        44     
       inv_mpu_dmp_motion_driver.o    640     0         16     
    +--+------------------------------+-------+---------+---------+
       Total:                         1460    82        60     
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     292     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1048    0         0      
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/iqmath/lib/ticlang/m0p/mathacl/iqmath.a
       _IQNtoF.o                      48      0         0      
       _IQNdiv.o                      24      0         0      
       _IQNmpy.o                      24      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         0      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3076    64        4      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2460    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       134       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17240   612       1704   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000045d8 records: 2, size/record: 8, table size: 16
	.data: load addr=00004560, load size=00000062 bytes, run addr=20200324, run size=00000185 bytes, compression=lzss
	.bss: load addr=000045d0, load size=00000008 bytes, run addr=20200000, run size=00000323 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000045c4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00001fc1     00004394     00004392   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             000043ac          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             000043b6          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             000043da          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00004408          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00001ca9     000043c4     000043c0   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000011d3     000043f4     000043f0   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             0000441a          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003ae1     00004420     0000441c   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[4 trampolines]
[9 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00004411  ADC0_IRQHandler                      
00004411  ADC1_IRQHandler                      
00004411  AES_IRQHandler                       
00004414  C$$EXIT                              
00004411  CANFD0_IRQHandler                    
00004411  DAC0_IRQHandler                      
00004381  DL_Common_delayCycles                
000032f1  DL_DMA_initChannel                   
00002fe1  DL_I2C_fillControllerTXFIFO          
0000365d  DL_I2C_flushControllerTXFIFO         
00003b7b  DL_I2C_setClockConfig                
000020a5  DL_SYSCTL_configSYSPLL               
00002d8d  DL_SYSCTL_setHFCLKSourceHFXTParams   
000033d1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002325  DL_Timer_initPWMMode                 
00003db5  DL_Timer_setCaptCompUpdateMethod     
000040a9  DL_Timer_setCaptureCompareOutCtl     
00004349  DL_Timer_setCaptureCompareValue      
00003dd1  DL_Timer_setClockConfig              
00003389  DL_UART_init                         
00004301  DL_UART_setClockConfig               
00004411  DMA_IRQHandler                       
20200300  Data_Accel                           
20200306  Data_Gyro                            
20200480  Data_MotorEncoder                    
20200490  Data_Motor_TarSpeed                  
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200488  Data_Tracker_Input                   
20200494  Data_Tracker_Offset                  
20200314  Data_Yaw                             
00004411  Default_Handler                      
00003c05  Delay                                
20200318  ExISR_Flag                           
202004a6  Flag_MPU6050_Ready                   
00004411  GROUP0_IRQHandler                    
00001655  GROUP1_IRQHandler                    
00004415  HOSTexit                             
00004411  HardFault_Handler                    
00004411  I2C0_IRQHandler                      
00004411  I2C1_IRQHandler                      
000031a9  Interrupt_Init                       
20200460  Motor                                
20200324  Motor_Back_Left                      
20200368  Motor_Back_Right                     
202003ac  Motor_Font_Left                      
202003f0  Motor_Font_Right                     
000026d1  Motor_GetSpeed                       
00002565  Motor_SetDuty                        
00002259  Motor_Start                          
00004411  NMI_Handler                          
0000394d  PID_IQ_Init                          
00001b85  PID_IQ_Prosc                         
00003415  PID_IQ_SetParams                     
00004411  PendSV_Handler                       
00004411  RTC_IRQHandler                       
000009cd  Read_Quad                            
0000441d  Reset_Handler                        
00004411  SPI0_IRQHandler                      
00004411  SPI1_IRQHandler                      
00004411  SVC_Handler                          
00003895  SYSCFG_DL_DMA_CH_RX_init             
00004169  SYSCFG_DL_DMA_CH_TX_init             
00004369  SYSCFG_DL_DMA_init                   
0000071d  SYSCFG_DL_GPIO_init                  
000030f9  SYSCFG_DL_I2C_MPU6050_init           
00002df1  SYSCFG_DL_I2C_OLED_init              
00002ab9  SYSCFG_DL_MotorBack_init             
00002b39  SYSCFG_DL_MotorFront_init            
00003041  SYSCFG_DL_SYSCTL_init                
00004359  SYSCFG_DL_SYSTICK_init               
0000292d  SYSCFG_DL_UART0_init                 
000038f5  SYSCFG_DL_init                       
00002775  SYSCFG_DL_initPower                  
00003151  Serial_Init                          
20200000  Serial_RxData                        
0000421f  SysGetTick                           
00002ca9  SysTick_Handler                      
00003ab9  SysTick_Increasment                  
00004375  Sys_GetTick                          
00004411  TIMA0_IRQHandler                     
00004411  TIMA1_IRQHandler                     
00004411  TIMG0_IRQHandler                     
00004411  TIMG12_IRQHandler                    
00004411  TIMG6_IRQHandler                     
00004411  TIMG7_IRQHandler                     
00004411  TIMG8_IRQHandler                     
00004313  TI_memcpy_small                      
0000261d  Task_Add                             
00002f81  Task_IdleFunction                    
00003459  Task_Init                            
00001ebd  Task_Motor_PID                       
00001019  Task_Start                           
0000309d  Task_Tracker                         
000017c1  Tracker_Read                         
00004411  UART0_IRQHandler                     
00004411  UART1_IRQHandler                     
00004411  UART2_IRQHandler                     
00004411  UART3_IRQHandler                     
00004181  _IQ24div                             
00004199  _IQ24mpy                             
000038c5  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000045d8  __TI_CINIT_Base                      
000045e8  __TI_CINIT_Limit                     
000045e8  __TI_CINIT_Warm                      
000045c4  __TI_Handler_Table_Base              
000045d0  __TI_Handler_Table_Limit             
00003711  __TI_auto_init_nobinit_nopinit       
00002bb9  __TI_decompress_lzss                 
00004325  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00004235  __TI_zero_init_nomemset              
000011d3  __adddf3                             
0000218b  __addsf3                             
00002cb1  __aeabi_d2f                          
000034e1  __aeabi_d2uiz                        
000011d3  __aeabi_dadd                         
00002eb9  __aeabi_dcmpeq                       
00002ef5  __aeabi_dcmpge                       
00002f09  __aeabi_dcmpgt                       
00002ee1  __aeabi_dcmple                       
00002ecd  __aeabi_dcmplt                       
00001ca9  __aeabi_ddiv                         
00001fc1  __aeabi_dmul                         
000011c9  __aeabi_dsub                         
20200498  __aeabi_errno                        
000043dd  __aeabi_errno_addr                   
00003565  __aeabi_f2d                          
000037c5  __aeabi_f2iz                         
0000218b  __aeabi_fadd                         
00002f1d  __aeabi_fcmpeq                       
00002f59  __aeabi_fcmpge                       
00002f6d  __aeabi_fcmpgt                       
00002f45  __aeabi_fcmple                       
00002f31  __aeabi_fcmplt                       
00002a35  __aeabi_fdiv                         
00002815  __aeabi_fmul                         
00002181  __aeabi_fsub                         
00003699  __aeabi_i2f                          
0000135b  __aeabi_idiv0                        
000043e5  __aeabi_memcpy                       
000043e5  __aeabi_memcpy4                      
000043e5  __aeabi_memcpy8                      
00003525  __aeabi_uidiv                        
00003525  __aeabi_uidivmod                     
ffffffff  __binit__                            
00002d25  __cmpdf2                             
0000374d  __cmpsf2                             
00001ca9  __divdf3                             
00002a35  __divsf3                             
00002d25  __eqdf2                              
0000374d  __eqsf2                              
00003565  __extendsfdf2                        
000037c5  __fixsfsi                            
000034e1  __fixunsdfsi                         
00003699  __floatsisf                          
00002c35  __gedf2                              
000036d5  __gesf2                              
00002c35  __gtdf2                              
000036d5  __gtsf2                              
00002d25  __ledf2                              
0000374d  __lesf2                              
00002d25  __ltdf2                              
0000374d  __ltsf2                              
UNDEFED   __mpu_init                           
00001fc1  __muldf3                             
00003789  __muldsi3                            
00002815  __mulsf3                             
00002d25  __nedf2                              
0000374d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000011c9  __subdf3                             
00002181  __subsf3                             
00002cb1  __truncdfsf2                         
00003ae1  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00004431  _system_pre_init                     
0000440b  abort                                
000000c1  asin                                 
000000c1  asinl                                
00000425  atan                                 
0000135d  atan2                                
0000135d  atan2l                               
00000425  atanl                                
ffffffff  binit                                
2020049c  delayTick                            
00000e25  dmp_read_fifo                        
202004a8  enable_group1_irq                    
00004520  hw                                   
00000000  interruptVectors                     
00003c25  main                                 
20200322  more                                 
00002e55  mpu6050_i2c_sda_unlock               
00001db5  mpu_read_fifo_stream                 
00000bf9  mpu_reset_fifo                       
0000191d  mspm0_i2c_read                       
000023e9  mspm0_i2c_write                      
00001a51  qsort                                
202002f0  quat                                 
000044d0  reg                                  
2020031c  sensor_timestamp                     
20200320  sensors                              
000014e5  sqrt                                 
000014e5  sqrtl                                
000044a8  test                                 
202004a0  uwTick                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  asin                                 
000000c1  asinl                                
00000200  __STACK_SIZE                         
00000425  atan                                 
00000425  atanl                                
0000071d  SYSCFG_DL_GPIO_init                  
000009cd  Read_Quad                            
00000bf9  mpu_reset_fifo                       
00000e25  dmp_read_fifo                        
00001019  Task_Start                           
000011c9  __aeabi_dsub                         
000011c9  __subdf3                             
000011d3  __adddf3                             
000011d3  __aeabi_dadd                         
0000135b  __aeabi_idiv0                        
0000135d  atan2                                
0000135d  atan2l                               
000014e5  sqrt                                 
000014e5  sqrtl                                
00001655  GROUP1_IRQHandler                    
000017c1  Tracker_Read                         
0000191d  mspm0_i2c_read                       
00001a51  qsort                                
00001b85  PID_IQ_Prosc                         
00001ca9  __aeabi_ddiv                         
00001ca9  __divdf3                             
00001db5  mpu_read_fifo_stream                 
00001ebd  Task_Motor_PID                       
00001fc1  __aeabi_dmul                         
00001fc1  __muldf3                             
000020a5  DL_SYSCTL_configSYSPLL               
00002181  __aeabi_fsub                         
00002181  __subsf3                             
0000218b  __addsf3                             
0000218b  __aeabi_fadd                         
00002259  Motor_Start                          
00002325  DL_Timer_initPWMMode                 
000023e9  mspm0_i2c_write                      
00002565  Motor_SetDuty                        
0000261d  Task_Add                             
000026d1  Motor_GetSpeed                       
00002775  SYSCFG_DL_initPower                  
00002815  __aeabi_fmul                         
00002815  __mulsf3                             
0000292d  SYSCFG_DL_UART0_init                 
00002a35  __aeabi_fdiv                         
00002a35  __divsf3                             
00002ab9  SYSCFG_DL_MotorBack_init             
00002b39  SYSCFG_DL_MotorFront_init            
00002bb9  __TI_decompress_lzss                 
00002c35  __gedf2                              
00002c35  __gtdf2                              
00002ca9  SysTick_Handler                      
00002cb1  __aeabi_d2f                          
00002cb1  __truncdfsf2                         
00002d25  __cmpdf2                             
00002d25  __eqdf2                              
00002d25  __ledf2                              
00002d25  __ltdf2                              
00002d25  __nedf2                              
00002d8d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002df1  SYSCFG_DL_I2C_OLED_init              
00002e55  mpu6050_i2c_sda_unlock               
00002eb9  __aeabi_dcmpeq                       
00002ecd  __aeabi_dcmplt                       
00002ee1  __aeabi_dcmple                       
00002ef5  __aeabi_dcmpge                       
00002f09  __aeabi_dcmpgt                       
00002f1d  __aeabi_fcmpeq                       
00002f31  __aeabi_fcmplt                       
00002f45  __aeabi_fcmple                       
00002f59  __aeabi_fcmpge                       
00002f6d  __aeabi_fcmpgt                       
00002f81  Task_IdleFunction                    
00002fe1  DL_I2C_fillControllerTXFIFO          
00003041  SYSCFG_DL_SYSCTL_init                
0000309d  Task_Tracker                         
000030f9  SYSCFG_DL_I2C_MPU6050_init           
00003151  Serial_Init                          
000031a9  Interrupt_Init                       
000032f1  DL_DMA_initChannel                   
00003389  DL_UART_init                         
000033d1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003415  PID_IQ_SetParams                     
00003459  Task_Init                            
000034e1  __aeabi_d2uiz                        
000034e1  __fixunsdfsi                         
00003525  __aeabi_uidiv                        
00003525  __aeabi_uidivmod                     
00003565  __aeabi_f2d                          
00003565  __extendsfdf2                        
0000365d  DL_I2C_flushControllerTXFIFO         
00003699  __aeabi_i2f                          
00003699  __floatsisf                          
000036d5  __gesf2                              
000036d5  __gtsf2                              
00003711  __TI_auto_init_nobinit_nopinit       
0000374d  __cmpsf2                             
0000374d  __eqsf2                              
0000374d  __lesf2                              
0000374d  __ltsf2                              
0000374d  __nesf2                              
00003789  __muldsi3                            
000037c5  __aeabi_f2iz                         
000037c5  __fixsfsi                            
00003895  SYSCFG_DL_DMA_CH_RX_init             
000038c5  _IQ24toF                             
000038f5  SYSCFG_DL_init                       
0000394d  PID_IQ_Init                          
00003ab9  SysTick_Increasment                  
00003ae1  _c_int00_noargs                      
00003b7b  DL_I2C_setClockConfig                
00003c05  Delay                                
00003c25  main                                 
00003db5  DL_Timer_setCaptCompUpdateMethod     
00003dd1  DL_Timer_setClockConfig              
000040a9  DL_Timer_setCaptureCompareOutCtl     
00004169  SYSCFG_DL_DMA_CH_TX_init             
00004181  _IQ24div                             
00004199  _IQ24mpy                             
0000421f  SysGetTick                           
00004235  __TI_zero_init_nomemset              
00004301  DL_UART_setClockConfig               
00004313  TI_memcpy_small                      
00004325  __TI_decompress_none                 
00004349  DL_Timer_setCaptureCompareValue      
00004359  SYSCFG_DL_SYSTICK_init               
00004369  SYSCFG_DL_DMA_init                   
00004375  Sys_GetTick                          
00004381  DL_Common_delayCycles                
000043dd  __aeabi_errno_addr                   
000043e5  __aeabi_memcpy                       
000043e5  __aeabi_memcpy4                      
000043e5  __aeabi_memcpy8                      
0000440b  abort                                
00004411  ADC0_IRQHandler                      
00004411  ADC1_IRQHandler                      
00004411  AES_IRQHandler                       
00004411  CANFD0_IRQHandler                    
00004411  DAC0_IRQHandler                      
00004411  DMA_IRQHandler                       
00004411  Default_Handler                      
00004411  GROUP0_IRQHandler                    
00004411  HardFault_Handler                    
00004411  I2C0_IRQHandler                      
00004411  I2C1_IRQHandler                      
00004411  NMI_Handler                          
00004411  PendSV_Handler                       
00004411  RTC_IRQHandler                       
00004411  SPI0_IRQHandler                      
00004411  SPI1_IRQHandler                      
00004411  SVC_Handler                          
00004411  TIMA0_IRQHandler                     
00004411  TIMA1_IRQHandler                     
00004411  TIMG0_IRQHandler                     
00004411  TIMG12_IRQHandler                    
00004411  TIMG6_IRQHandler                     
00004411  TIMG7_IRQHandler                     
00004411  TIMG8_IRQHandler                     
00004411  UART0_IRQHandler                     
00004411  UART1_IRQHandler                     
00004411  UART2_IRQHandler                     
00004411  UART3_IRQHandler                     
00004414  C$$EXIT                              
00004415  HOSTexit                             
0000441d  Reset_Handler                        
00004431  _system_pre_init                     
000044a8  test                                 
000044d0  reg                                  
00004520  hw                                   
000045c4  __TI_Handler_Table_Base              
000045d0  __TI_Handler_Table_Limit             
000045d8  __TI_CINIT_Base                      
000045e8  __TI_CINIT_Limit                     
000045e8  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  quat                                 
20200300  Data_Accel                           
20200306  Data_Gyro                            
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200314  Data_Yaw                             
20200318  ExISR_Flag                           
2020031c  sensor_timestamp                     
20200320  sensors                              
20200322  more                                 
20200324  Motor_Back_Left                      
20200368  Motor_Back_Right                     
202003ac  Motor_Font_Left                      
202003f0  Motor_Font_Right                     
20200460  Motor                                
20200480  Data_MotorEncoder                    
20200488  Data_Tracker_Input                   
20200490  Data_Motor_TarSpeed                  
20200494  Data_Tracker_Offset                  
20200498  __aeabi_errno                        
2020049c  delayTick                            
202004a0  uwTick                               
202004a6  Flag_MPU6050_Ready                   
202004a8  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[224 symbols]
