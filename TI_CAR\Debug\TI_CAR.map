******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 22:43:56 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000524d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005fd8  0001a028  R  X
  SRAM                  20200000   00008000  000006a8  00007958  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005fd8   00005fd8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005bf0   00005bf0    r-x .text
  00005cb0    00005cb0    000002a0   000002a0    r-- .rodata
  00005f50    00005f50    00000088   00000088    r-- .cinit
20200000    20200000    000004a9   00000000    rw-
  20200000    20200000    00000323   00000000    rw- .bss
  20200324    20200324    00000185   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005bf0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002bc     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000013a8    0000022c     MPU6050.o (.text.Read_Quad)
                  000015d4    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001800    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001a20    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001c14    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001df0    000001b0     Task.o (.text.Task_Start)
                  00001fa0    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00002132    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002134    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000022bc    00000170            : e_sqrt.c.obj (.text.sqrt)
                  0000242c    0000016c     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002598    0000015c     Tracker.o (.text.Tracker_Read)
                  000026f4    00000154     Task_App.o (.text.Task_Motor_PID)
                  00002848    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002984    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00002ab8    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00002bec    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00002d10    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002e30    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002f3c    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003044    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003148    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000322c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00003308    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000033e0    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000034b8    000000d0     Task_App.o (.text.Task_Serial)
                  00003588    000000cc     Motor.o (.text.Motor_Start)
                  00003654    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00003718    000000b8     Motor.o (.text.Motor_SetDirc)
                  000037d0    000000b8     Motor.o (.text.Motor_SetDuty)
                  00003888    000000b4     Task.o (.text.Task_Add)
                  0000393c    000000a4     Motor.o (.text.Motor_GetSpeed)
                  000039e0    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00003a82    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003a84    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003b24    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  00003bb0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00003c3c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003cc8    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00003d54    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00003dd8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003e5c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003ede    00000002     --HOLE-- [fill = 0]
                  00003ee0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003f5c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00003fd0    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00004044    00000070     Serial.o (.text.MyPrintf_DMA)
                  000040b4    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000411c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00004182    00000002     --HOLE-- [fill = 0]
                  00004184    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000041e8    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000424c    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  000042b0    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00004312    00000002     --HOLE-- [fill = 0]
                  00004314    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00004376    00000002     --HOLE-- [fill = 0]
                  00004378    00000060     Task_App.o (.text.Task_IdleFunction)
                  000043d8    00000060     Task_App.o (.text.Task_Init)
                  00004438    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00004496    00000002     --HOLE-- [fill = 0]
                  00004498    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000044f4    0000005c     Task_App.o (.text.Task_Tracker)
                  00004550    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000045ac    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00004608    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00004660    00000058     Serial.o (.text.Serial_Init)
                  000046b8    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00004710    00000058            : _printfi.c.obj (.text._pconv_f)
                  00004768    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000047be    00000002     --HOLE-- [fill = 0]
                  000047c0    00000054     Interrupt.o (.text.Interrupt_Init)
                  00004814    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00004866    00000002     --HOLE-- [fill = 0]
                  00004868    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  000048b8    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00004908    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004954    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000049a0    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000049ea    00000002     --HOLE-- [fill = 0]
                  000049ec    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004a34    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00004a7c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004ac0    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00004b04    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00004b46    00000002     --HOLE-- [fill = 0]
                  00004b48    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004b88    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004bc8    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004c08    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00004c48    0000003e     Task.o (.text.Task_CMP)
                  00004c86    00000002     --HOLE-- [fill = 0]
                  00004c88    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004cc4    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004d00    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00004d3c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00004d78    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004db4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004df0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004e2c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00004e66    00000002     --HOLE-- [fill = 0]
                  00004e68    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00004ea2    00000002     --HOLE-- [fill = 0]
                  00004ea4    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00004edc    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004f10    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004f44    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00004f74    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00004fa4    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00004fd4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00005004    00000030            : vsnprintf.c.obj (.text._outs)
                  00005034    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00005060    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  0000508c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000050b8    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  000050e2    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  0000510a    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00005132    00000002     --HOLE-- [fill = 0]
                  00005134    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  0000515c    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00005184    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000051ac    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000051d4    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000051fc    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00005224    00000028     SysTick.o (.text.SysTick_Increasment)
                  0000524c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00005274    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  0000529a    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  000052c0    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000052e6    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000530c    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00005330    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00005354    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00005376    00000002     --HOLE-- [fill = 0]
                  00005378    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00005398    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000053b8    00000020     SysTick.o (.text.Delay)
                  000053d8    00000020     main.o (.text.main)
                  000053f8    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00005416    00000002     --HOLE-- [fill = 0]
                  00005418    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00005436    00000002     --HOLE-- [fill = 0]
                  00005438    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00005454    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00005470    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  0000548c    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  000054a8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000054c4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000054e0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000054fc    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00005518    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00005534    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00005550    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000556c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005588    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000055a4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000055c0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000055dc    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000055f4    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  0000560c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00005624    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  0000563c    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00005654    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  0000566c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005684    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  0000569c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000056b4    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000056cc    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000056e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000056fc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005714    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  0000572c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00005744    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  0000575c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00005774    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  0000578c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000057a4    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000057bc    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000057d4    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000057ec    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00005804    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  0000581c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00005834    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000584c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00005864    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  0000587c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00005894    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000058ac    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  000058c4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000058dc    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  000058f4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  0000590c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00005924    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  0000593c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00005954    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  0000596c    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00005984    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  0000599c    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000059b2    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  000059c8    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  000059de    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  000059f4    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005a0a    00000016     SysTick.o (.text.SysGetTick)
                  00005a20    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00005a36    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00005a4a    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00005a5e    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00005a72    00000002     --HOLE-- [fill = 0]
                  00005a74    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00005a88    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00005a9c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00005ab0    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00005ac4    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00005ad8    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005aec    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005b00    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005b14    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00005b26    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005b38    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005b4a    00000002     --HOLE-- [fill = 0]
                  00005b4c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005b5c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005b6c    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00005b7c    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005b8c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00005b9a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005ba8    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00005bb6    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00005bc2    00000002     --HOLE-- [fill = 0]
                  00005bc4    0000000c     SysTick.o (.text.Sys_GetTick)
                  00005bd0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00005bda    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005be4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00005bf4    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005bfe    00000002     --HOLE-- [fill = 0]
                  00005c00    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00005c10    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005c1a    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005c24    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005c2e    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00005c38    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00005c48    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00005c50    00000008     Interrupt.o (.text.SysTick_Handler)
                  00005c58    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005c60    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00005c68    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005c6e    00000002     --HOLE-- [fill = 0]
                  00005c70    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00005c80    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00005c86    00000006            : exit.c.obj (.text:abort)
                  00005c8c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005c90    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00005c94    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00005c98    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005c9c    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005cac    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00005f50    00000088     
                  00005f50    00000062     (.cinit..data.load) [load image, compression = lzss]
                  00005fb2    00000002     --HOLE-- [fill = 0]
                  00005fb4    0000000c     (__TI_handler_table)
                  00005fc0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005fc8    00000010     (__TI_cinit_table)

.rodata    0    00005cb0    000002a0     
                  00005cb0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005db1    00000007     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00005db8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00005df8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00005e20    00000028     inv_mpu.o (.rodata.test)
                  00005e48    00000026     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00005e6e    00000023     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00005e91    0000001e     inv_mpu.o (.rodata.reg)
                  00005eaf    00000001     --HOLE-- [fill = 0]
                  00005eb0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00005ec8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00005ee0    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00005ef1    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00005f02    0000000c     inv_mpu.o (.rodata.hw)
                  00005f0e    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00005f18    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  00005f20    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00005f28    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00005f30    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00005f36    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  00005f39    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00005f3c    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00005f3e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005f40    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00005f42    0000000e     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000323     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    00000010     (.common:quat)
                  20200300    00000006     (.common:Data_Accel)
                  20200306    00000006     (.common:Data_Gyro)
                  2020030c    00000004     (.common:Data_Pitch)
                  20200310    00000004     (.common:Data_Roll)
                  20200314    00000004     (.common:Data_Yaw)
                  20200318    00000004     (.common:ExISR_Flag)
                  2020031c    00000004     (.common:sensor_timestamp)
                  20200320    00000002     (.common:sensors)
                  20200322    00000001     (.common:more)

.data      0    20200324    00000185     UNINITIALIZED
                  20200324    00000044     Motor.o (.data.Motor_Back_Left)
                  20200368    00000044     Motor.o (.data.Motor_Back_Right)
                  202003ac    00000044     Motor.o (.data.Motor_Font_Left)
                  202003f0    00000044     Motor.o (.data.Motor_Font_Right)
                  20200434    0000002c     inv_mpu.o (.data.st)
                  20200460    00000010     Task_App.o (.data.Motor)
                  20200470    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200480    00000008     Task_App.o (.data.Data_MotorEncoder)
                  20200488    00000008     Task_App.o (.data.Data_Tracker_Input)
                  20200490    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  20200494    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  20200498    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  2020049c    00000004     SysTick.o (.data.delayTick)
                  202004a0    00000004     SysTick.o (.data.uwTick)
                  202004a4    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004a6    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004a7    00000001     Task.o (.data.Task_Num)
                  202004a8    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3490    126       0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3530    318       0      
                                                               
    .\APP\Src\
       Task_App.o                     832     94        42     
       Interrupt.o                    626     0         6      
    +--+------------------------------+-------+---------+---------+
       Total:                         1458    94        48     
                                                               
    .\BSP\Src\
       MPU6050.o                      1884    0         47     
       Motor.o                        804     0         272    
       Serial.o                       404     0         512    
       Task.o                         674     0         241    
       PID_IQMath.o                   402     0         0      
       Tracker.o                      370     0         0      
       SysTick.o                      106     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         4644    0         1080   
                                                               
    .\DMP\
       inv_mpu.o                      820     82        44     
       inv_mpu_dmp_motion_driver.o    640     0         16     
    +--+------------------------------+-------+---------+---------+
       Total:                         1460    82        60     
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1112    0         0      
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                      48      0         0      
       _IQNdiv.o                      24      0         0      
       _IQNmpy.o                      24      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8246    355       4      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2944    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       134       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   23494   983       1704   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005fc8 records: 2, size/record: 8, table size: 16
	.data: load addr=00005f50, load size=00000062 bytes, run addr=20200324, run size=00000185 bytes, compression=lzss
	.bss: load addr=00005fc0, load size=00000008 bytes, run addr=20200000, run size=00000323 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005fb4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001fa1     00005be4     00005be2   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00003149     00005c00     00005bfc   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005c18          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005c2c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005c4e          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00005c84          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00002e31     00005c38     00005c36   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00001fab     00005c70     00005c6c   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005c96          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   0000524d     00005c9c     00005c98   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00005c8d  ADC0_IRQHandler                      
00005c8d  ADC1_IRQHandler                      
00005c8d  AES_IRQHandler                       
00005c90  C$$EXIT                              
00005c8d  CANFD0_IRQHandler                    
00005c8d  DAC0_IRQHandler                      
00005bd1  DL_Common_delayCycles                
00004909  DL_DMA_initChannel                   
00004439  DL_I2C_fillControllerTXFIFO          
00004d01  DL_I2C_flushControllerTXFIFO         
000052e7  DL_I2C_setClockConfig                
0000322d  DL_SYSCTL_configSYSPLL               
00004185  DL_SYSCTL_setHFCLKSourceHFXTParams   
00004a7d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003045  DL_Timer_initFourCCPWMMode           
00005589  DL_Timer_setCaptCompUpdateMethod     
0000587d  DL_Timer_setCaptureCompareOutCtl     
00005b5d  DL_Timer_setCaptureCompareValue      
000055a5  DL_Timer_setClockConfig              
000049ed  DL_UART_init                         
00005b15  DL_UART_setClockConfig               
00005c8d  DMA_IRQHandler                       
20200300  Data_Accel                           
20200306  Data_Gyro                            
20200480  Data_MotorEncoder                    
20200490  Data_Motor_TarSpeed                  
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200488  Data_Tracker_Input                   
20200494  Data_Tracker_Offset                  
20200314  Data_Yaw                             
00005c8d  Default_Handler                      
000053b9  Delay                                
20200318  ExISR_Flag                           
202004a6  Flag_MPU6050_Ready                   
00005c8d  GROUP0_IRQHandler                    
0000242d  GROUP1_IRQHandler                    
00005c91  HOSTexit                             
00005c8d  HardFault_Handler                    
00005c8d  I2C0_IRQHandler                      
00005c8d  I2C1_IRQHandler                      
000047c1  Interrupt_Init                       
20200460  Motor                                
20200324  Motor_Back_Left                      
20200368  Motor_Back_Right                     
202003ac  Motor_Font_Left                      
202003f0  Motor_Font_Right                     
0000393d  Motor_GetSpeed                       
000037d1  Motor_SetDuty                        
00003589  Motor_Start                          
00004045  MyPrintf_DMA                         
00005c8d  NMI_Handler                          
000050b9  PID_IQ_Init                          
00002bed  PID_IQ_Prosc                         
00004ac1  PID_IQ_SetParams                     
00005c8d  PendSV_Handler                       
00005c8d  RTC_IRQHandler                       
000013a9  Read_Quad                            
00005c99  Reset_Handler                        
00005c8d  SPI0_IRQHandler                      
00005c8d  SPI1_IRQHandler                      
00005c8d  SVC_Handler                          
00004f75  SYSCFG_DL_DMA_CH_RX_init             
0000593d  SYSCFG_DL_DMA_CH_TX_init             
00005bb7  SYSCFG_DL_DMA_init                   
000010ed  SYSCFG_DL_GPIO_init                  
00004609  SYSCFG_DL_I2C_MPU6050_init           
000041e9  SYSCFG_DL_I2C_OLED_init              
00003b25  SYSCFG_DL_MotorBack_init             
00003bb1  SYSCFG_DL_MotorFront_init            
00004499  SYSCFG_DL_SYSCTL_init                
00005b6d  SYSCFG_DL_SYSTICK_init               
00003d55  SYSCFG_DL_UART0_init                 
00005035  SYSCFG_DL_init                       
00003a85  SYSCFG_DL_initPower                  
00004661  Serial_Init                          
20200000  Serial_RxData                        
00005a0b  SysGetTick                           
00005c51  SysTick_Handler                      
00005225  SysTick_Increasment                  
00005bc5  Sys_GetTick                          
00005c8d  TIMA0_IRQHandler                     
00005c8d  TIMA1_IRQHandler                     
00005c8d  TIMG0_IRQHandler                     
00005c8d  TIMG12_IRQHandler                    
00005c8d  TIMG6_IRQHandler                     
00005c8d  TIMG7_IRQHandler                     
00005c8d  TIMG8_IRQHandler                     
00005b27  TI_memcpy_small                      
00005ba9  TI_memset_small                      
00003889  Task_Add                             
00004379  Task_IdleFunction                    
000043d9  Task_Init                            
000026f5  Task_Motor_PID                       
000034b9  Task_Serial                          
00001df1  Task_Start                           
000044f5  Task_Tracker                         
00002599  Tracker_Read                         
00005c8d  UART0_IRQHandler                     
00005c8d  UART1_IRQHandler                     
00005c8d  UART2_IRQHandler                     
00005c8d  UART3_IRQHandler                     
00005955  _IQ24div                             
0000596d  _IQ24mpy                             
00004fa5  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005fc8  __TI_CINIT_Base                      
00005fd8  __TI_CINIT_Limit                     
00005fd8  __TI_CINIT_Warm                      
00005fb4  __TI_Handler_Table_Base              
00005fc0  __TI_Handler_Table_Limit             
00004df1  __TI_auto_init_nobinit_nopinit       
00003ee1  __TI_decompress_lzss                 
00005b39  __TI_decompress_none                 
000046b9  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005a21  __TI_zero_init_nomemset              
00001fab  __adddf3                             
000033eb  __addsf3                             
00005cb0  __aeabi_ctype_table_                 
00005cb0  __aeabi_ctype_table_C                
00003fd1  __aeabi_d2f                          
000049a1  __aeabi_d2iz                         
00004b05  __aeabi_d2uiz                        
00001fab  __aeabi_dadd                         
000042b1  __aeabi_dcmpeq                       
000042ed  __aeabi_dcmpge                       
00004301  __aeabi_dcmpgt                       
000042d9  __aeabi_dcmple                       
000042c5  __aeabi_dcmplt                       
00002e31  __aeabi_ddiv                         
00003149  __aeabi_dmul                         
00001fa1  __aeabi_dsub                         
20200498  __aeabi_errno                        
00005c59  __aeabi_errno_addr                   
00004b89  __aeabi_f2d                          
00004ea5  __aeabi_f2iz                         
000033eb  __aeabi_fadd                         
00004315  __aeabi_fcmpeq                       
00004351  __aeabi_fcmpge                       
00004365  __aeabi_fcmpgt                       
0000433d  __aeabi_fcmple                       
00004329  __aeabi_fcmplt                       
00003e5d  __aeabi_fdiv                         
00003c3d  __aeabi_fmul                         
000033e1  __aeabi_fsub                         
0000508d  __aeabi_i2d                          
00004d79  __aeabi_i2f                          
00004769  __aeabi_idiv                         
00002133  __aeabi_idiv0                        
00004769  __aeabi_idivmod                      
00003a83  __aeabi_ldiv0                        
00005419  __aeabi_llsl                         
00005331  __aeabi_lmul                         
00005c61  __aeabi_memcpy                       
00005c61  __aeabi_memcpy4                      
00005c61  __aeabi_memcpy8                      
00005b8d  __aeabi_memset                       
00005b8d  __aeabi_memset4                      
00005b8d  __aeabi_memset8                      
00004b49  __aeabi_uidiv                        
00004b49  __aeabi_uidivmod                     
00005aed  __aeabi_uldivmod                     
00005419  __ashldi3                            
ffffffff  __binit__                            
000040b5  __cmpdf2                             
00004e2d  __cmpsf2                             
00002e31  __divdf3                             
00003e5d  __divsf3                             
000040b5  __eqdf2                              
00004e2d  __eqsf2                              
00004b89  __extendsfdf2                        
000049a1  __fixdfsi                            
00004ea5  __fixsfsi                            
00004b05  __fixunsdfsi                         
0000508d  __floatsidf                          
00004d79  __floatsisf                          
00003f5d  __gedf2                              
00004db5  __gesf2                              
00003f5d  __gtdf2                              
00004db5  __gtsf2                              
000040b5  __ledf2                              
00004e2d  __lesf2                              
000040b5  __ltdf2                              
00004e2d  __ltsf2                              
UNDEFED   __mpu_init                           
00003149  __muldf3                             
00005331  __muldi3                             
00004e69  __muldsi3                            
00003c3d  __mulsf3                             
000040b5  __nedf2                              
00004e2d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001fa1  __subdf3                             
000033e1  __subsf3                             
00003fd1  __truncdfsf2                         
000039e1  __udivmoddi4                         
0000524d  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00005cad  _system_pre_init                     
00005c87  abort                                
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002135  atan2                                
00002135  atan2l                               
00000df5  atanl                                
00004bc9  atoi                                 
ffffffff  binit                                
2020049c  delayTick                            
00001a21  dmp_read_fifo                        
202004a8  enable_group1_irq                    
00004551  frexp                                
00004551  frexpl                               
00005f02  hw                                   
00000000  interruptVectors                     
00003309  ldexp                                
00003309  ldexpl                               
000053d9  main                                 
00005355  memccpy                              
20200322  more                                 
0000424d  mpu6050_i2c_sda_unlock               
00002f3d  mpu_read_fifo_stream                 
000015d5  mpu_reset_fifo                       
00002985  mspm0_i2c_read                       
00003655  mspm0_i2c_write                      
00002ab9  qsort                                
202002f0  quat                                 
00005e91  reg                                  
00003309  scalbn                               
00003309  scalbnl                              
2020031c  sensor_timestamp                     
20200320  sensors                              
000022bd  sqrt                                 
000022bd  sqrtl                                
00005e20  test                                 
202004a0  uwTick                               
00004c09  vsnprintf                            
00005b7d  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  SYSCFG_DL_GPIO_init                  
000013a9  Read_Quad                            
000015d5  mpu_reset_fifo                       
00001a21  dmp_read_fifo                        
00001df1  Task_Start                           
00001fa1  __aeabi_dsub                         
00001fa1  __subdf3                             
00001fab  __adddf3                             
00001fab  __aeabi_dadd                         
00002133  __aeabi_idiv0                        
00002135  atan2                                
00002135  atan2l                               
000022bd  sqrt                                 
000022bd  sqrtl                                
0000242d  GROUP1_IRQHandler                    
00002599  Tracker_Read                         
000026f5  Task_Motor_PID                       
00002985  mspm0_i2c_read                       
00002ab9  qsort                                
00002bed  PID_IQ_Prosc                         
00002e31  __aeabi_ddiv                         
00002e31  __divdf3                             
00002f3d  mpu_read_fifo_stream                 
00003045  DL_Timer_initFourCCPWMMode           
00003149  __aeabi_dmul                         
00003149  __muldf3                             
0000322d  DL_SYSCTL_configSYSPLL               
00003309  ldexp                                
00003309  ldexpl                               
00003309  scalbn                               
00003309  scalbnl                              
000033e1  __aeabi_fsub                         
000033e1  __subsf3                             
000033eb  __addsf3                             
000033eb  __aeabi_fadd                         
000034b9  Task_Serial                          
00003589  Motor_Start                          
00003655  mspm0_i2c_write                      
000037d1  Motor_SetDuty                        
00003889  Task_Add                             
0000393d  Motor_GetSpeed                       
000039e1  __udivmoddi4                         
00003a83  __aeabi_ldiv0                        
00003a85  SYSCFG_DL_initPower                  
00003b25  SYSCFG_DL_MotorBack_init             
00003bb1  SYSCFG_DL_MotorFront_init            
00003c3d  __aeabi_fmul                         
00003c3d  __mulsf3                             
00003d55  SYSCFG_DL_UART0_init                 
00003e5d  __aeabi_fdiv                         
00003e5d  __divsf3                             
00003ee1  __TI_decompress_lzss                 
00003f5d  __gedf2                              
00003f5d  __gtdf2                              
00003fd1  __aeabi_d2f                          
00003fd1  __truncdfsf2                         
00004045  MyPrintf_DMA                         
000040b5  __cmpdf2                             
000040b5  __eqdf2                              
000040b5  __ledf2                              
000040b5  __ltdf2                              
000040b5  __nedf2                              
00004185  DL_SYSCTL_setHFCLKSourceHFXTParams   
000041e9  SYSCFG_DL_I2C_OLED_init              
0000424d  mpu6050_i2c_sda_unlock               
000042b1  __aeabi_dcmpeq                       
000042c5  __aeabi_dcmplt                       
000042d9  __aeabi_dcmple                       
000042ed  __aeabi_dcmpge                       
00004301  __aeabi_dcmpgt                       
00004315  __aeabi_fcmpeq                       
00004329  __aeabi_fcmplt                       
0000433d  __aeabi_fcmple                       
00004351  __aeabi_fcmpge                       
00004365  __aeabi_fcmpgt                       
00004379  Task_IdleFunction                    
000043d9  Task_Init                            
00004439  DL_I2C_fillControllerTXFIFO          
00004499  SYSCFG_DL_SYSCTL_init                
000044f5  Task_Tracker                         
00004551  frexp                                
00004551  frexpl                               
00004609  SYSCFG_DL_I2C_MPU6050_init           
00004661  Serial_Init                          
000046b9  __TI_ltoa                            
00004769  __aeabi_idiv                         
00004769  __aeabi_idivmod                      
000047c1  Interrupt_Init                       
00004909  DL_DMA_initChannel                   
000049a1  __aeabi_d2iz                         
000049a1  __fixdfsi                            
000049ed  DL_UART_init                         
00004a7d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004ac1  PID_IQ_SetParams                     
00004b05  __aeabi_d2uiz                        
00004b05  __fixunsdfsi                         
00004b49  __aeabi_uidiv                        
00004b49  __aeabi_uidivmod                     
00004b89  __aeabi_f2d                          
00004b89  __extendsfdf2                        
00004bc9  atoi                                 
00004c09  vsnprintf                            
00004d01  DL_I2C_flushControllerTXFIFO         
00004d79  __aeabi_i2f                          
00004d79  __floatsisf                          
00004db5  __gesf2                              
00004db5  __gtsf2                              
00004df1  __TI_auto_init_nobinit_nopinit       
00004e2d  __cmpsf2                             
00004e2d  __eqsf2                              
00004e2d  __lesf2                              
00004e2d  __ltsf2                              
00004e2d  __nesf2                              
00004e69  __muldsi3                            
00004ea5  __aeabi_f2iz                         
00004ea5  __fixsfsi                            
00004f75  SYSCFG_DL_DMA_CH_RX_init             
00004fa5  _IQ24toF                             
00005035  SYSCFG_DL_init                       
0000508d  __aeabi_i2d                          
0000508d  __floatsidf                          
000050b9  PID_IQ_Init                          
00005225  SysTick_Increasment                  
0000524d  _c_int00_noargs                      
000052e7  DL_I2C_setClockConfig                
00005331  __aeabi_lmul                         
00005331  __muldi3                             
00005355  memccpy                              
000053b9  Delay                                
000053d9  main                                 
00005419  __aeabi_llsl                         
00005419  __ashldi3                            
00005589  DL_Timer_setCaptCompUpdateMethod     
000055a5  DL_Timer_setClockConfig              
0000587d  DL_Timer_setCaptureCompareOutCtl     
0000593d  SYSCFG_DL_DMA_CH_TX_init             
00005955  _IQ24div                             
0000596d  _IQ24mpy                             
00005a0b  SysGetTick                           
00005a21  __TI_zero_init_nomemset              
00005aed  __aeabi_uldivmod                     
00005b15  DL_UART_setClockConfig               
00005b27  TI_memcpy_small                      
00005b39  __TI_decompress_none                 
00005b5d  DL_Timer_setCaptureCompareValue      
00005b6d  SYSCFG_DL_SYSTICK_init               
00005b7d  wcslen                               
00005b8d  __aeabi_memset                       
00005b8d  __aeabi_memset4                      
00005b8d  __aeabi_memset8                      
00005ba9  TI_memset_small                      
00005bb7  SYSCFG_DL_DMA_init                   
00005bc5  Sys_GetTick                          
00005bd1  DL_Common_delayCycles                
00005c51  SysTick_Handler                      
00005c59  __aeabi_errno_addr                   
00005c61  __aeabi_memcpy                       
00005c61  __aeabi_memcpy4                      
00005c61  __aeabi_memcpy8                      
00005c87  abort                                
00005c8d  ADC0_IRQHandler                      
00005c8d  ADC1_IRQHandler                      
00005c8d  AES_IRQHandler                       
00005c8d  CANFD0_IRQHandler                    
00005c8d  DAC0_IRQHandler                      
00005c8d  DMA_IRQHandler                       
00005c8d  Default_Handler                      
00005c8d  GROUP0_IRQHandler                    
00005c8d  HardFault_Handler                    
00005c8d  I2C0_IRQHandler                      
00005c8d  I2C1_IRQHandler                      
00005c8d  NMI_Handler                          
00005c8d  PendSV_Handler                       
00005c8d  RTC_IRQHandler                       
00005c8d  SPI0_IRQHandler                      
00005c8d  SPI1_IRQHandler                      
00005c8d  SVC_Handler                          
00005c8d  TIMA0_IRQHandler                     
00005c8d  TIMA1_IRQHandler                     
00005c8d  TIMG0_IRQHandler                     
00005c8d  TIMG12_IRQHandler                    
00005c8d  TIMG6_IRQHandler                     
00005c8d  TIMG7_IRQHandler                     
00005c8d  TIMG8_IRQHandler                     
00005c8d  UART0_IRQHandler                     
00005c8d  UART1_IRQHandler                     
00005c8d  UART2_IRQHandler                     
00005c8d  UART3_IRQHandler                     
00005c90  C$$EXIT                              
00005c91  HOSTexit                             
00005c99  Reset_Handler                        
00005cad  _system_pre_init                     
00005cb0  __aeabi_ctype_table_                 
00005cb0  __aeabi_ctype_table_C                
00005e20  test                                 
00005e91  reg                                  
00005f02  hw                                   
00005fb4  __TI_Handler_Table_Base              
00005fc0  __TI_Handler_Table_Limit             
00005fc8  __TI_CINIT_Base                      
00005fd8  __TI_CINIT_Limit                     
00005fd8  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  quat                                 
20200300  Data_Accel                           
20200306  Data_Gyro                            
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200314  Data_Yaw                             
20200318  ExISR_Flag                           
2020031c  sensor_timestamp                     
20200320  sensors                              
20200322  more                                 
20200324  Motor_Back_Left                      
20200368  Motor_Back_Right                     
202003ac  Motor_Font_Left                      
202003f0  Motor_Font_Right                     
20200460  Motor                                
20200480  Data_MotorEncoder                    
20200488  Data_Tracker_Input                   
20200490  Data_Motor_TarSpeed                  
20200494  Data_Tracker_Offset                  
20200498  __aeabi_errno                        
2020049c  delayTick                            
202004a0  uwTick                               
202004a6  Flag_MPU6050_Ready                   
202004a8  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[257 symbols]
