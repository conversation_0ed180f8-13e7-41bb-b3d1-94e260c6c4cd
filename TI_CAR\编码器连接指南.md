# TI MSPM0G3507 编码器连接指南

## 概述

本文档指导您连接4个电机的增量式编码器，实现闭环速度控制。系统使用AB相编码器，通过外部中断检测编码器脉冲，计算电机转速。

## 编码器类型与规格

### 推荐编码器规格
- **类型**: 增量式AB相编码器
- **分辨率**: 260脉冲/转 (已在代码中定义)
- **输出**: 5V TTL电平或3.3V CMOS电平
- **相位差**: A相和B相相差90°
- **工作电压**: 3.3V-5V

### 编码器工作原理
```
A相: ┌─┐   ┌─┐   ┌─┐   ┌─┐
     │ │   │ │   │ │   │ │
     └─┘   └─┘   └─┘   └─┘

B相:   ┌─┐   ┌─┐   ┌─┐   ┌─┐
       │ │   │ │   │ │   │ │
       └─┘   └─┘   └─┘   └─┘

正转: A相超前B相90°
反转: B相超前A相90°
```

## 硬件连接

### 编码器引脚分配表

| 电机位置 | A相引脚 | B相引脚 | 物理引脚 | 功能描述 |
|----------|---------|---------|----------|----------|
| 左前轮 | PA27 | PB10 | 55, 47 | 编码器A/B相输入 |
| 右前轮 | PA26 | PB11 | 54, 48 | 编码器A/B相输入 |
| 左后轮 | PA25 | PB12 | 53, 52 | 编码器A/B相输入 |
| 右后轮 | PA24 | PB13 | 51, 53 | 编码器A/B相输入 |

### 详细连接图

```
TI MSPM0G3507 LaunchPad          编码器模块
┌─────────────────────┐         ┌─────────────────────┐
│                     │         │                     │
│  PA27 (Pin 55) ─────────────────→ 左前轮编码器A相    │
│  PB10 (Pin 47) ─────────────────→ 左前轮编码器B相    │
│                     │         │                     │
│  PA26 (Pin 54) ─────────────────→ 右前轮编码器A相    │
│  PB11 (Pin 48) ─────────────────→ 右前轮编码器B相    │
│                     │         │                     │
│  PA25 (Pin 53) ─────────────────→ 左后轮编码器A相    │
│  PB12 (Pin 52) ─────────────────→ 左后轮编码器B相    │
│                     │         │                     │
│  PA24 (Pin 51) ─────────────────→ 右后轮编码器A相    │
│  PB13 (Pin 53) ─────────────────→ 右后轮编码器B相    │
│                     │         │                     │
│  3.3V ──────────────────────────→ VCC (所有编码器)   │
│  GND  ──────────────────────────→ GND (所有编码器)   │
└─────────────────────┐         └─────────────────────┘
```

### 电源连接
- **VCC**: 连接3.3V电源
- **GND**: 连接地线
- **A相/B相**: 连接对应的MCU引脚

## 软件配置状态

### ✅ 已完成配置

#### 1. GPIO配置 (empty.syscfg)
```c
// A相引脚配置 - 外部中断输入
GPIO3.$name = "SPD_READER_A";
GPIO3.port = "PORTA";
GPIO3.associatedPins[0].direction = "INPUT";
GPIO3.associatedPins[0].interruptEn = true;
GPIO3.associatedPins[0].polarity = "RISE";           // 上升沿触发
GPIO3.associatedPins[0].internalResistor = "PULL_DOWN";

// B相引脚配置 - 数字输入
GPIO4.$name = "SPD_READER_B";
GPIO4.port = "PORTB";
GPIO4.associatedPins[0].direction = "INPUT";
GPIO4.associatedPins[0].internalResistor = "PULL_DOWN";
```

#### 2. 中断处理 (Interrupt.c)
```c
void GROUP1_IRQHandler(void)
{
    // A相上升沿中断处理
    if (ISR_IS_GPIO(SPD_READER_A_FONT_LEFT_A_PIN)) {
        // 检测B相电平判断方向
        if (GET_RDR_B_VAL(SPD_READER_B_FONT_LEFT_B_PIN)) 
            (*Motor_Font_Left.Motor_Encoder_Addr)++;    // 正转
        else 
            (*Motor_Font_Left.Motor_Encoder_Addr)--;    // 反转
    }
    // ... 其他3个电机类似处理
}
```

#### 3. 速度计算 (Motor.c)
```c
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time)
{
    _iq Encoder_Value = _IQ(*Motor->Motor_Encoder_Addr);  // 获取编码计数
    *Motor->Motor_Encoder_Addr = 0;                       // 清零计数器
    
    _iq Speed = _IQdiv(Encoder_Value, Interval_Time);     // 计算速度
    Motor->Motor_PID_Instance.Acutal_Now = Speed;         // 更新PID反馈
}
```

## 连接步骤

### 第一步: 准备编码器模块
1. 确认编码器输出电平(3.3V或5V)
2. 识别A相、B相、VCC、GND引脚
3. 如果是5V编码器，需要电平转换电路

### 第二步: 连接电源
```
编码器VCC → MCU 3.3V
编码器GND → MCU GND
```

### 第三步: 连接信号线
按照上述引脚分配表连接A相和B相信号线：

**左前轮编码器**:
```
编码器A相 → PA27 (Pin 55)
编码器B相 → PB10 (Pin 47)
```

**右前轮编码器**:
```
编码器A相 → PA26 (Pin 54)
编码器B相 → PB11 (Pin 48)
```

**左后轮编码器**:
```
编码器A相 → PA25 (Pin 53)
编码器B相 → PB12 (Pin 52)
```

**右后轮编码器**:
```
编码器A相 → PA24 (Pin 51)
编码器B相 → PB13 (Pin 53)
```

### 第四步: 检查连接
1. 用万用表测试电源电压
2. 检查信号线连接是否正确
3. 确认编码器固定牢固

## 测试验证

### 1. 编码器计数测试
```c
// 在Task_Serial中添加编码器计数输出
void Task_Serial(void *para)
{
    MyPrintf_DMA("Encoder: LF=%d, RF=%d, LB=%d, RB=%d\r\n",
                 Data_MotorEncoder[0], Data_MotorEncoder[1],
                 Data_MotorEncoder[2], Data_MotorEncoder[3]);
}
```

### 2. 手动转动测试
1. 启用串口调试输出
2. 手动转动电机轴
3. 观察编码器计数变化
4. 正转计数应增加，反转计数应减少

### 3. 速度反馈测试
```c
// 在Task_Serial中添加速度输出
void Task_Serial(void *para)
{
    MyPrintf_DMA("Speed: LF=%.2f, RF=%.2f, LB=%.2f, RB=%.2f\r\n",
                 _IQtoF(Motor[0]->Motor_PID_Instance.Acutal_Now),
                 _IQtoF(Motor[1]->Motor_PID_Instance.Acutal_Now),
                 _IQtoF(Motor[2]->Motor_PID_Instance.Acutal_Now),
                 _IQtoF(Motor[3]->Motor_PID_Instance.Acutal_Now));
}
```

## 故障排除

### 问题1: 编码器无计数
**可能原因**:
- 电源未连接或电压不足
- 信号线连接错误
- 编码器损坏

**解决方法**:
- 检查电源连接和电压
- 核对引脚连接
- 更换编码器测试

### 问题2: 计数方向错误
**可能原因**:
- A相和B相接反
- 编码器安装方向错误

**解决方法**:
- 交换A相和B相连接
- 调整编码器安装方向

### 问题3: 计数不稳定
**可能原因**:
- 信号线干扰
- 编码器固定不牢
- 电源纹波过大

**解决方法**:
- 使用屏蔽线
- 加固编码器安装
- 添加电源滤波电容

### 问题4: PID控制异常
**可能原因**:
- 编码器分辨率设置错误
- 速度计算单位不匹配

**解决方法**:
- 确认编码器分辨率为260脉冲/转
- 检查速度计算公式

## 注意事项

### 1. 电气特性
- 确保编码器输出电平与MCU兼容
- 5V编码器需要电平转换
- 信号线尽量短，避免干扰

### 2. 机械安装
- 编码器轴与电机轴同心
- 固定牢固，避免松动
- 保护编码器免受冲击

### 3. 软件配置
- 编码器分辨率已设置为260脉冲/转
- 中断优先级已配置
- 速度计算周期为50ms

## 完成检查清单

- [ ] 编码器电源连接正确(3.3V)
- [ ] 4个编码器A相信号线连接正确
- [ ] 4个编码器B相信号线连接正确
- [ ] 编码器机械安装牢固
- [ ] 串口调试功能启用
- [ ] 手动转动测试通过
- [ ] 编码器计数方向正确
- [ ] PID速度反馈正常

完成以上连接后，您的小车将具备完整的速度闭环控制能力，解决电机全速运转的问题。
