# TI MSPM0G3507 灰度循迹模块实现

## 概述

本文档详细介绍了基于TI MSPM0G3507微控制器的8路灰度传感器循迹系统的硬件接线和软件实现原理。该系统采用红外反射式传感器阵列，通过加权平均算法计算位置偏差，实现高精度的循迹控制。

## 硬件配置

### 1. 传感器规格
- **传感器类型**: 8路红外反射式灰度传感器
- **传感器间距**: 1.5cm
- **检测范围**: ±5.25cm (总宽度10.5cm)
- **工作原理**: 红外LED发射，光敏二极管接收反射光
- **输出逻辑**: 
  - 检测到黑线: 输出低电平 (0V)
  - 检测到白色: 输出高电平 (3.3V)

### 2. GPIO引脚分配

| 传感器编号 | GPIO引脚 | 物理引脚 | 位置权重(cm) | 功能描述 |
|-----------|----------|----------|-------------|----------|
| Tracker_1 | PB0      | 47       | -5.25       | 最左侧传感器 |
| Tracker_2 | PB1      | 48       | -3.75       | 左侧传感器2 |
| Tracker_3 | PB4      | 52       | -2.25       | 左侧传感器3 |
| Tracker_4 | PB5      | 53       | -0.75       | 左中传感器 |
| Tracker_5 | PB15     | 3        | +0.75       | 右中传感器 |
| Tracker_6 | PB16     | 4        | +2.25       | 右侧传感器3 |
| Tracker_7 | PB17     | 14       | +3.75       | 右侧传感器2 |
| Tracker_8 | PB18     | 15       | +5.25       | 最右侧传感器 |

### 3. 硬件接线图

```
TI MSPM0G3507 LaunchPad          8路灰度传感器模块
┌─────────────────────┐         ┌─────────────────────┐
│                     │         │  VCC ←──── 3.3V     │
│  PB0  (Pin 47) ────────────────→ OUT1               │
│  PB1  (Pin 48) ────────────────→ OUT2               │
│  PB4  (Pin 52) ────────────────→ OUT3               │
│  PB5  (Pin 53) ────────────────→ OUT4               │
│  PB15 (Pin 3)  ────────────────→ OUT5               │
│  PB16 (Pin 4)  ────────────────→ OUT6               │
│  PB17 (Pin 14) ────────────────→ OUT7               │
│  PB18 (Pin 15) ────────────────→ OUT8               │
│                     │         │  GND ←──── GND      │
│  3.3V ──────────────────────────→ VCC               │
│  GND  ──────────────────────────→ GND               │
└─────────────────────┘         └─────────────────────┘
```

### 4. GPIO配置参数

```c
// GPIO配置 (在empty.syscfg中定义)
GPIO6.$name = "Tracker";
GPIO6.port = "PORTB";
GPIO6.associatedPins.create(8);

// 每个引脚配置为数字输入，上拉电阻
GPIO6.associatedPins[x].direction = "INPUT";
GPIO6.associatedPins[x].ioStructure = "SD";
GPIO6.associatedPins[x].internalResistor = "PULL_UP";
```

## 软件实现

### 1. 核心算法 - 加权平均法

系统采用加权平均算法计算黑线的位置偏差：

```c
// 传感器位置权重计算公式
sensor_pos = (i - 3.5) × 1.5cm

// 其中 i 为传感器索引 (0-7)
// 对应位置: -5.25, -3.75, -2.25, -0.75, 0.75, 2.25, 3.75, 5.25 cm
```

### 2. 位置偏差计算流程

1. **读取传感器状态**: 通过GPIO读取8路传感器的数字信号
2. **加权求和**: 对检测到黑线的传感器进行位置加权
3. **平均计算**: 计算加权平均位置作为黑线中心位置
4. **滤波处理**: 使用一阶低通滤波器平滑偏差值

### 3. 关键函数实现

#### Tracker_Read() - 传感器读取函数
```c
bool Tracker_Read(uint8_t *tck_ptr, _iq *offset_ptr)
{
    // 1. 读取8路传感器状态
    tck_ptr[0] = DL_GPIO_readPins(Tracker_PORT, Tracker__1_PIN);
    tck_ptr[1] = DL_GPIO_readPins(Tracker_PORT, Tracker__2_PIN);
    // ... 其他传感器
    
    // 2. 加权平均计算
    _iq pos_sum = _IQ(0);
    uint8_t cnt = 0;
    
    for (uint8_t i = 0; i < 8; i++) {
        if (tck_ptr[i] == TRACK_ON) {
            _iq sensor_pos = _IQmpy(_IQ(i - 3.5f), DIS_INRERVAL);
            pos_sum += sensor_pos;
            cnt++;
        }
    }
    
    // 3. 计算最终偏差
    if (cnt > 0) {
        *offset_ptr = _IQdiv(pos_sum, _IQ(cnt));
        return true;
    }
    return false;
}
```

#### Task_Tracker() - 循迹任务函数
```c
void Task_Tracker(void *para)
{
    const _iq Filter_Value = _IQ(0.7); // 滤波系数
    _iq Temp = 0;
    
    // 读取传感器并计算偏差
    bool res = Tracker_Read(Data_Tracker_Input, &Temp);
    
    if (res == true) {
        // 一阶低通滤波
        Data_Tracker_Offset = _IQmpy(Temp, Filter_Value) + 
                             _IQmpy((_IQ(1) - Filter_Value), Data_Tracker_Offset);
    }
}
```

### 4. 数据结构定义

```c
// 全局变量定义 (在Task_App.c中)
uint8_t Data_Tracker_Input[8] = {TRACK_OFF}; // 8路传感器状态
_iq Data_Tracker_Offset = _IQ(0);            // 位置偏差值
PID_IQ_Def_t Data_Tracker_PID;               // 转向PID控制器

// 宏定义 (在Tracker.h中)
#define TRACK_ON    1    // 检测到黑线
#define TRACK_OFF   0    // 未检测到黑线
#define DIS_INRERVAL _IQ(1.5)  // 传感器间距1.5cm
```

## 系统特性

### 1. 技术优势
- **高精度**: 1.5cm传感器间距，理论精度可达±0.75cm
- **快速响应**: 20ms任务周期，实时性好
- **抗干扰**: 数字滤波算法，减少噪声影响
- **低功耗**: GPIO数字输入，功耗极低

### 2. 算法特点
- **加权平均**: 多传感器融合，提高检测精度
- **容错处理**: 无线检测时保持上次偏差值
- **滤波平滑**: 0.7滤波系数，平衡响应速度和稳定性

### 3. 应用场景
- **直线循迹**: 基础循迹功能
- **弯道处理**: 大角度转弯检测
- **交叉路口**: 多线检测和路径选择
- **断线恢复**: 短暂断线后的重新捕获

## 调试与优化

### 1. 传感器标定
- 调整传感器高度 (建议2-5mm)
- 校准黑白线检测阈值
- 验证传感器间距精度

### 2. 参数调优
- 滤波系数: 0.5-0.9 (响应速度vs稳定性)
- 任务周期: 10-50ms (精度vs系统负载)
- PID参数: 根据机械特性调整

### 3. 故障排除
- 检查GPIO引脚配置
- 验证传感器供电电压
- 测试传感器输出逻辑
- 监控偏差值变化趋势

## 故障排除 - 电机全速运转问题

### 问题现象
无论灰度传感器检测到什么状况，小车4个轮子都会全速运转，无法正常循迹。

### 可能原因分析

#### 1. **PID参数设置问题** ⚠️ 高概率
**问题**: PID参数过大导致输出饱和
```c
// 当前PID参数 (在Motor_Start()中设置)
PID_IQ_SetParams(&Motor_Font_Left.Motor_PID_Instance, 2.0f, 0.5f, 0.1f);
// Kp=2.0过大，Ki=0.5积分作用强，可能导致输出饱和
```

**解决方案**:
```c
// 建议调整为更保守的参数
PID_IQ_SetParams(&Motor_Font_Left.Motor_PID_Instance, 0.5f, 0.1f, 0.05f);
PID_IQ_SetParams(&Motor_Back_Left.Motor_PID_Instance, 0.5f, 0.1f, 0.05f);
PID_IQ_SetParams(&Motor_Back_Right.Motor_PID_Instance, 0.5f, 0.1f, 0.05f);
PID_IQ_SetParams(&Motor_Font_Right.Motor_PID_Instance, 0.5f, 0.1f, 0.05f);
```

#### 2. **目标速度设置过高** ⚠️ 高概率
**问题**: 基础目标速度30可能过高
```c
_iq Data_Motor_TarSpeed = _IQ(30); //目标基础速度可能过高
```

**解决方案**:
```c
_iq Data_Motor_TarSpeed = _IQ(10); //降低到10进行测试
```

#### 3. **编码器反馈异常** ⚠️ 中等概率
**问题**: 编码器没有正确反馈速度，导致PID认为实际速度为0
- 编码器连接问题
- 编码器中断未正确配置
- 编码器计数方向错误

**检查方法**:
```c
// 在Task_Serial中添加调试输出
void Task_Serial(void *para)
{
    MyPrintf_DMA("Actual:%.2f, Target:%.2f, Out:%.2f, Encoder:%d\r\n",
                 _IQtoF(Motor[0]->Motor_PID_Instance.Acutal_Now),
                 _IQtoF(Motor[0]->Motor_PID_Instance.Target),
                 _IQtoF(Motor[0]->Motor_PID_Instance.Out),
                 Data_MotorEncoder[0]);
}
```

#### 4. **循迹偏差计算异常** ⚠️ 中等概率
**问题**: 传感器读取逻辑或偏差计算错误
- 传感器极性设置错误(TRACK_ON/TRACK_OFF)
- GPIO读取返回值异常
- 偏差值异常导致差速控制失效

**检查方法**:
```c
// 在Task_Tracker中添加调试输出
void Task_Tracker(void *para)
{
    const _iq Filter_Value = _IQ(0.7);
    _iq Temp = 0;
    bool res = Tracker_Read(Data_Tracker_Input, &Temp);

    // 添加调试输出
    MyPrintf_DMA("Sensors:%d%d%d%d%d%d%d%d, Offset:%.2f\r\n",
                 Data_Tracker_Input[0], Data_Tracker_Input[1],
                 Data_Tracker_Input[2], Data_Tracker_Input[3],
                 Data_Tracker_Input[4], Data_Tracker_Input[5],
                 Data_Tracker_Input[6], Data_Tracker_Input[7],
                 _IQtoF(Data_Tracker_Offset));

    if (res == true) {
        Data_Tracker_Offset = _IQmpy(Temp, Filter_Value) +
                             _IQmpy((_IQ(1) - Filter_Value), Data_Tracker_Offset);
    }
}
```

#### 5. **转向系数设置问题** ⚠️ 低概率
**问题**: 转向系数INDEX=0.2可能过小，导致差速效果不明显
```c
#define INDEX 0.2f //转向调试系数可能过小
```

#### 6. **PID输出限幅问题** ⚠️ 低概率
**问题**: PID输出限制在±100，但可能仍然过高
```c
#define PID_IQ_OUT_MAX_VALUE _IQ(100.0) //PID输出最大值
```

**解决方案**: 临时降低输出限幅
```c
#define PID_IQ_OUT_MAX_VALUE _IQ(50.0) //降低到50进行测试
```

### 调试步骤建议

#### 第一步: 启用串口调试
```c
// 在Task_Init()中启用串口任务
Task_Add("Serial", Task_Serial, 50, NULL, 2);
```

#### 第二步: 降低PID参数
```c
// 使用保守的PID参数
PID_IQ_SetParams(&Motor_Font_Left.Motor_PID_Instance, 0.3f, 0.05f, 0.01f);
```

#### 第三步: 降低目标速度
```c
_iq Data_Motor_TarSpeed = _IQ(5); //先用很低的速度测试
```

#### 第四步: 检查传感器读取
观察串口输出的传感器状态和偏差值是否正常。

#### 第五步: 逐步调试
1. 先测试直线行驶(无循迹)
2. 再测试循迹功能
3. 逐步提高速度和PID参数

### 快速验证方法

#### 方法1: 禁用循迹，测试固定速度
```c
// 在Task_Motor_PID中临时注释差速控制
void Task_Motor_PID(void *para)
{
    // 获取电机速度
    for (uint8_t i = 0; i < 4; i++) {
        Motor_GetSpeed(Motor[i], 50);
    }

    // 临时使用固定低速度测试
    _iq Fixed_Speed = _IQ(5);
    Motor_Font_Left.Motor_PID_Instance.Target = Fixed_Speed;
    Motor_Back_Left.Motor_PID_Instance.Target = Fixed_Speed;
    Motor_Font_Right.Motor_PID_Instance.Target = Fixed_Speed;
    Motor_Back_Right.Motor_PID_Instance.Target = Fixed_Speed;

    // PID计算和输出...
}
```

#### 方法2: 开环测试
```c
// 临时跳过PID，直接设置PWM
void Task_Motor_PID(void *para)
{
    // 直接设置低PWM值测试
    Motor_SetDuty(&Motor_Font_Left, 10.0f);
    Motor_SetDuty(&Motor_Font_Right, 10.0f);
    Motor_SetDuty(&Motor_Back_Left, 10.0f);
    Motor_SetDuty(&Motor_Back_Right, 10.0f);
}
```

## 总结

该灰度循迹系统采用8路传感器阵列和加权平均算法，实现了高精度、快响应的循迹功能。通过合理的硬件设计和优化的软件算法，系统能够稳定可靠地完成各种循迹任务，为智能小车提供了强大的路径跟踪能力。

当遇到电机全速运转问题时，应优先检查PID参数设置和目标速度配置，通过串口调试输出分析具体原因，并采用逐步调试的方法定位问题。
