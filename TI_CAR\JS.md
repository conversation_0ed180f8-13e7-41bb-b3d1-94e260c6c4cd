# TI MSPM0G3507 智能循迹小车工程介绍

## 工程概述

本工程是基于 TI MSPM0G3507 微控制器开发的智能循迹小车系统，采用模块化设计，实现了多传感器融合、PID控制、任务调度等功能。

## 硬件平台

- **主控芯片**: TI MSPM0G3507 (ARM Cortex-M0+)
- **开发板**: LP-MSPM0G3507 LaunchPad
- **传感器**: MPU6050六轴陀螺仪、8路循迹传感器
- **执行器**: 4个直流电机（前左、前右、后左、后右）
- **显示**: 128x64 OLED显示屏(SSD1306)
- **通信**: UART串口、I2C总线

## 系统架构

```
├── main.c                 # 主程序入口
├── APP/                   # 应用层
│   ├── Inc/
│   │   ├── SysConfig.h    # 系统配置头文件
│   │   ├── Task_App.h     # 应用任务头文件
│   │   └── Interrupt.h    # 中断处理头文件
│   └── Src/
│       ├── Task_App.c     # 应用任务实现
│       └── Interrupt.c    # 中断处理实现
├── BSP/                   # 板级支持包
│   ├── Inc/               # 驱动头文件
│   └── Src/               # 驱动实现文件
└── DMP/                   # MPU6050 DMP库
```

## 核心功能模块

### 1. 任务调度系统 (Task.c/h)
- **功能**: 实现多任务协作调度，支持优先级管理
- **特性**: 
  - 支持最多10个任务并发
  - 基于时间片轮转的抢占式调度
  - 任务状态管理(活跃/暂停/非活跃)
  - 动态性能监控和调整
- **接口**:
  ```c
  void Task_Add(const char *taskName, taskFunction_t pfunc, uint16_t time_ms, void *para, uint8_t priority);
  void Task_Start(systick_get func);
  bool Task_Suspend(const char *taskName);
  bool Task_Resume(const char *taskName);
  ```

### 2. 电机控制系统 (Motor.c/h)
- **功能**: 四轮独立驱动控制，支持PWM调速和方向控制
- **特性**:
  - 四个电机独立PID速度控制
  - 编码器反馈闭环控制
  - 差速转向算法实现
- **接口**:
  ```c
  void Motor_Start(void);                                    # 启动所有电机
  bool Motor_SetDuty(MOTOR_Def_t *Motor, float value);      # 设置电机占空比
  bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time);   # 获取电机速度
  ```

### 3. PID控制算法 (PID_IQMath.c/h)
- **功能**: 基于TI IQMath库的高精度PID控制器
- **特性**:
  - 定点数运算，提高计算效率
  - 变速积分算法，防止积分饱和
  - 积分限幅和输出限幅保护
- **接口**:
  ```c
  void PID_IQ_Init(PID_IQ_Def_t *pid);     # 初始化PID参数
  void PID_IQ_Prosc(PID_IQ_Def_t *pid);   # PID计算处理
  ```

### 4. 循迹传感器 (Tracker.c/h)
- **功能**: 8路红外循迹传感器数据处理
- **特性**:
  - 加权平均算法计算位置偏差
  - 传感器间距1.5cm，检测范围±5.25cm
  - 支持线性插值提高精度
- **接口**:
  ```c
  bool Tracker_Read(uint8_t *tck_ptr, _iq *offset_ptr);  # 读取传感器并计算偏差
  ```

### 5. MPU6050姿态传感器 (MPU6050.c/h)
- **功能**: 六轴陀螺仪加速度计数据融合
- **特性**:
  - 集成DMP数字运动处理器
  - 实时输出俯仰角、横滚角、偏航角
  - I2C通信，支持总线恢复机制
- **接口**:
  ```c
  void MPU6050_Init(void);    # 初始化MPU6050
  void Read_Quad(void);       # 读取四元数并计算姿态角
  ```

### 6. OLED显示系统 (OLED.c/h)
- **功能**: 128x64像素OLED显示屏驱动
- **特性**:
  - 基于SSD1306控制器
  - 支持8x16和6x8字体显示
  - I2C接口，显存页式管理
- **接口**:
  ```c
  void OLED_Init(void);                                           # 初始化OLED
  void OLED_Printf(uint8_t x, uint8_t y, uint8_t size, char *format, ...);  # 格式化显示
  ```

### 7. 串口通信 (Serial.c/h)
- **功能**: UART串口数据收发，支持DMA传输
- **特性**:
  - DMA非阻塞发送，提高效率
  - 格式化输出，便于调试
  - 波形数据输出，支持上位机分析
- **接口**:
  ```c
  void Serial_Init(void);                           # 初始化串口DMA
  uint16_t MyPrintf_DMA(char *format, ...);        # DMA格式化发送
  ```

### 8. 系统时基 (SysTick.c/h)
- **功能**: 系统时钟节拍和延时功能
- **特性**:
  - 1ms精度系统时基
  - 非阻塞延时函数
  - 时间戳获取接口
- **接口**:
  ```c
  uint32_t Sys_GetTick(void);    # 获取系统时基
  void Delay(uint32_t xms);      # 毫秒级延时
  ```

### 9. 按键LED (Key_Led.c/h)
- **功能**: 按键输入检测和LED状态控制
- **接口**:
  ```c
  uint8_t Key_Read(void);        # 读取按键状态(未消抖)
  LED_BOARD_ON()/LED_BOARD_OFF() # LED控制宏
  ```

## 应用任务说明

系统运行6个主要任务，按优先级排序：

1. **Motor任务** (50ms, 优先级0): 电机PID控制和差速转向
2. **Tracker任务** (10ms, 优先级1): 循迹传感器读取和偏差计算  
3. **Serial任务** (50ms, 优先级2): 串口数据发送和波形输出
4. **LED任务** (100ms, 优先级3): LED状态指示
5. **OLED任务** (50ms, 优先级4): 显示屏信息更新
6. **Key任务** (20ms, 优先级5): 按键检测和处理

## 控制算法

### 循迹控制流程
1. 循迹传感器读取 → 位置偏差计算
2. 偏差值经过滤波处理 → 差速转向控制
3. 左右轮速度调整 → PID速度环控制
4. PWM输出 → 电机驱动

### 差速转向算法
```c
_iq Steering_Adjustment = _IQmpy(Data_Tracker_Offset, _IQ(INDEX));
_iq Left_Speed = Data_Motor_TarSpeed - Steering_Adjustment;   # 左轮减速
_iq Right_Speed = Data_Motor_TarSpeed + Steering_Adjustment;  # 右轮加速
```

## 配置参数

- **电机编码值**: 260脉冲/圈 (MOTOR_FULL_VALUE)
- **转向系数**: 0.2 (INDEX)
- **目标速度**: 30 (Data_Motor_TarSpeed)
- **传感器间距**: 1.5cm (DIS_INRERVAL)
- **滤波系数**: 0.7 (Filter_Value)

## 编译和使用

1. **开发环境**: TI Code Composer Studio (CCS)
2. **编译**: 直接在CCS中编译工程
3. **下载**: 通过XDS-110仿真器下载到LaunchPad
4. **调试**: 支持在线调试和串口波形输出

## 扩展功能

- 支持MPU6050姿态反馈控制
- 可扩展蓝牙/WiFi无线控制
- 预留传感器接口便于功能扩展
- 模块化设计便于移植到其他平台

---
*本工程展示了嵌入式系统的完整开发流程，包含传感器驱动、控制算法、任务调度等核心技术，适合学习和二次开发。*
