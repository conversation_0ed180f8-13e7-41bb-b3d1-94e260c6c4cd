******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Mon Jul  7 17:25:29 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000e41


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000011f0  0001ee10  R  X
  SRAM                  20200000   00008000  000003ef  00007c11  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000011f0   000011f0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000010b0   000010b0    r-x .text
  00001170    00001170    00000050   00000050    r-- .rodata
  000011c0    000011c0    00000030   00000030    r-- .cinit
20200000    20200000    000001ef   00000000    rw-
  20200000    20200000    000001e0   00000000    rw- .bss
  202001e0    202001e0    0000000f   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000010b0     
                  000000c0    000001b0     Task.o (.text.Task_Start)
                  00000270    00000134     libc.a : qsort.c.obj (.text.qsort)
                  000003a4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000004a8    000000dc                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00000584    000000c4     Motor.o (.text.Motor_SetPWM)
                  00000648    000000b4     Task.o (.text.Task_Add)
                  000006fc    00000094     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000790    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  0000081c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  000008a8    00000088     Task_App.o (.text.Task_Key)
                  00000930    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000009b4    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000a30    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00000a94    0000005c     Task_App.o (.text.Task_Init)
                  00000af0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000b48    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00000b98    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000be0    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00000c24    00000040     Motor.o (.text.Motor_Start)
                  00000c64    0000003e     Task.o (.text.Task_CMP)
                  00000ca2    00000002     Task.o (.text.Task_IdleFunction)
                  00000ca4    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00000ce0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000d1c    00000038     Task_App.o (.text.Task_LED)
                  00000d54    00000036     Task_App.o (.text.Task_Motor)
                  00000d8a    00000002     --HOLE-- [fill = 0]
                  00000d8c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00000dc0    00000030     Key_Led.o (.text.Key_Read)
                  00000df0    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00000e18    00000028     SysTick.o (.text.SysTick_Handler)
                  00000e40    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000e68    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00000e88    00000020     main.o (.text.main)
                  00000ea8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00000ec4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00000ee0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00000efc    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00000f18    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00000f34    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000f50    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00000f68    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00000f80    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00000f98    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00000fb0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00000fc8    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00000fe0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00000ff8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00001010    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00001028    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001040    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00001058    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  0000106e    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00001084    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00001098    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000010ac    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000010c0    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000010d4    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000010e8    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000010fa    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000110c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000111c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000112c    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  0000113c    0000000c     SysTick.o (.text.Sys_GetTick)
                  00001148    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00001152    00000002     --HOLE-- [fill = 0]
                  00001154    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000115c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001160    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001164    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001168    00000004            : exit.c.obj (.text:abort)
                  0000116c    00000004     --HOLE-- [fill = 0]

.cinit     0    000011c0    00000030     
                  000011c0    0000000c     (__TI_handler_table)
                  000011cc    0000000b     (.cinit..data.load) [load image, compression = lzss]
                  000011d7    00000001     --HOLE-- [fill = 0]
                  000011d8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000011e0    00000010     (__TI_cinit_table)

.rodata    0    00001170    00000050     
                  00001170    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00001198    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  000011a0    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  000011a8    00000006     Task_App.o (.rodata.str1.115332825834609149281)
                  000011ae    00000004     Task_App.o (.rodata.str1.171900814140190138471)
                  000011b2    00000004     Task_App.o (.rodata.str1.67400646179352630301)
                  000011b6    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  000011b9    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  000011bc    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001e0     UNINITIALIZED
                  20200000    000001e0     Task.o (.bss.Task_Schedule)

.data      0    202001e0    0000000f     UNINITIALIZED
                  202001e0    00000004     Task_App.o (.data.Data_MotorPWM)
                  202001e4    00000004     SysTick.o (.data.delayTick)
                  202001e8    00000004     SysTick.o (.data.uwTick)
                  202001ec    00000001     Task_App.o (.data.Flag_LED)
                  202001ed    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  202001ee    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             1400   62        0      
       startup_mspm0g350x_ticlang.o   8      192       0      
       main.o                         32     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1440   254       0      
                                                              
    .\APP\Src\
       Task_App.o                     382    14        6      
    +--+------------------------------+------+---------+---------+
       Total:                         382    14        6      
                                                              
    .\BSP\Src\
       Task.o                         676    0         481    
       Motor.o                        284    0         0      
       Key_Led.o                      70     0         0      
       SysTick.o                      52     0         8      
    +--+------------------------------+------+---------+---------+
       Total:                         1082   0         489    
                                                              
    D:/TI/CCSTUDIO_Theia/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388    0         0      
       dl_timer.o                     356    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         754    0         0      
                                                              
    D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       qsort.c.obj                    308    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         598    0         0      
                                                              
    D:\TI\CCSTUDIO_Theia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      47        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   4264   315       1007   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000011e0 records: 2, size/record: 8, table size: 16
	.data: load addr=000011cc, load size=0000000b bytes, run addr=202001e0, run size=0000000f bytes, compression=lzss
	.bss: load addr=000011d8, load size=00000008 bytes, run addr=20200000, run size=000001e0 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000011c0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000115d  ADC0_IRQHandler                      
0000115d  ADC1_IRQHandler                      
0000115d  AES_IRQHandler                       
00001168  C$$EXIT                              
0000115d  CANFD0_IRQHandler                    
0000115d  DAC0_IRQHandler                      
00001149  DL_Common_delayCycles                
000004a9  DL_SYSCTL_configSYSPLL               
00000a31  DL_SYSCTL_setHFCLKSourceHFXTParams   
00000be1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000003a5  DL_Timer_initFourCCPWMMode           
00000efd  DL_Timer_setCaptCompUpdateMethod     
00001029  DL_Timer_setCaptureCompareOutCtl     
0000111d  DL_Timer_setCaptureCompareValue      
00000f19  DL_Timer_setClockConfig              
0000115d  DMA_IRQHandler                       
202001e0  Data_MotorPWM                        
0000115d  Default_Handler                      
202001ec  Flag_LED                             
0000115d  GROUP0_IRQHandler                    
0000115d  GROUP1_IRQHandler                    
0000115d  HardFault_Handler                    
0000115d  I2C0_IRQHandler                      
0000115d  I2C1_IRQHandler                      
00000dc1  Key_Read                             
00000585  Motor_SetPWM                         
00000c25  Motor_Start                          
0000115d  NMI_Handler                          
0000115d  PendSV_Handler                       
0000115d  RTC_IRQHandler                       
00001161  Reset_Handler                        
0000115d  SPI0_IRQHandler                      
0000115d  SPI1_IRQHandler                      
0000115d  SVC_Handler                          
000006fd  SYSCFG_DL_GPIO_init                  
00000791  SYSCFG_DL_MotorBack_init             
0000081d  SYSCFG_DL_MotorFront_init            
00000b99  SYSCFG_DL_SYSCTL_init                
0000112d  SYSCFG_DL_SYSTICK_init               
00000f35  SYSCFG_DL_init                       
00000af1  SYSCFG_DL_initPower                  
00000e19  SysTick_Handler                      
0000113d  Sys_GetTick                          
0000115d  TIMA0_IRQHandler                     
0000115d  TIMA1_IRQHandler                     
0000115d  TIMG0_IRQHandler                     
0000115d  TIMG12_IRQHandler                    
0000115d  TIMG6_IRQHandler                     
0000115d  TIMG7_IRQHandler                     
0000115d  TIMG8_IRQHandler                     
000010e9  TI_memcpy_small                      
00000649  Task_Add                             
00000ca3  Task_IdleFunction                    
00000a95  Task_Init                            
000008a9  Task_Key                             
00000d1d  Task_LED                             
00000d55  Task_Motor                           
000000c1  Task_Start                           
0000115d  UART0_IRQHandler                     
0000115d  UART1_IRQHandler                     
0000115d  UART2_IRQHandler                     
0000115d  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000011e0  __TI_CINIT_Base                      
000011f0  __TI_CINIT_Limit                     
000011f0  __TI_CINIT_Warm                      
000011c0  __TI_Handler_Table_Base              
000011cc  __TI_Handler_Table_Limit             
00000ce1  __TI_auto_init_nobinit_nopinit       
000009b5  __TI_decompress_lzss                 
000010fb  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000106f  __TI_zero_init_nomemset              
00001155  __aeabi_memcpy                       
00001155  __aeabi_memcpy4                      
00001155  __aeabi_memcpy8                      
ffffffff  __binit__                            
UNDEFED   __mpu_init                           
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000e41  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00001165  _system_pre_init                     
00001169  abort                                
ffffffff  binit                                
202001e4  delayTick                            
00000000  interruptVectors                     
00000e89  main                                 
00000271  qsort                                
202001e8  uwTick                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  Task_Start                           
00000200  __STACK_SIZE                         
00000271  qsort                                
000003a5  DL_Timer_initFourCCPWMMode           
000004a9  DL_SYSCTL_configSYSPLL               
00000585  Motor_SetPWM                         
00000649  Task_Add                             
000006fd  SYSCFG_DL_GPIO_init                  
00000791  SYSCFG_DL_MotorBack_init             
0000081d  SYSCFG_DL_MotorFront_init            
000008a9  Task_Key                             
000009b5  __TI_decompress_lzss                 
00000a31  DL_SYSCTL_setHFCLKSourceHFXTParams   
00000a95  Task_Init                            
00000af1  SYSCFG_DL_initPower                  
00000b99  SYSCFG_DL_SYSCTL_init                
00000be1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000c25  Motor_Start                          
00000ca3  Task_IdleFunction                    
00000ce1  __TI_auto_init_nobinit_nopinit       
00000d1d  Task_LED                             
00000d55  Task_Motor                           
00000dc1  Key_Read                             
00000e19  SysTick_Handler                      
00000e41  _c_int00_noargs                      
00000e89  main                                 
00000efd  DL_Timer_setCaptCompUpdateMethod     
00000f19  DL_Timer_setClockConfig              
00000f35  SYSCFG_DL_init                       
00001029  DL_Timer_setCaptureCompareOutCtl     
0000106f  __TI_zero_init_nomemset              
000010e9  TI_memcpy_small                      
000010fb  __TI_decompress_none                 
0000111d  DL_Timer_setCaptureCompareValue      
0000112d  SYSCFG_DL_SYSTICK_init               
0000113d  Sys_GetTick                          
00001149  DL_Common_delayCycles                
00001155  __aeabi_memcpy                       
00001155  __aeabi_memcpy4                      
00001155  __aeabi_memcpy8                      
0000115d  ADC0_IRQHandler                      
0000115d  ADC1_IRQHandler                      
0000115d  AES_IRQHandler                       
0000115d  CANFD0_IRQHandler                    
0000115d  DAC0_IRQHandler                      
0000115d  DMA_IRQHandler                       
0000115d  Default_Handler                      
0000115d  GROUP0_IRQHandler                    
0000115d  GROUP1_IRQHandler                    
0000115d  HardFault_Handler                    
0000115d  I2C0_IRQHandler                      
0000115d  I2C1_IRQHandler                      
0000115d  NMI_Handler                          
0000115d  PendSV_Handler                       
0000115d  RTC_IRQHandler                       
0000115d  SPI0_IRQHandler                      
0000115d  SPI1_IRQHandler                      
0000115d  SVC_Handler                          
0000115d  TIMA0_IRQHandler                     
0000115d  TIMA1_IRQHandler                     
0000115d  TIMG0_IRQHandler                     
0000115d  TIMG12_IRQHandler                    
0000115d  TIMG6_IRQHandler                     
0000115d  TIMG7_IRQHandler                     
0000115d  TIMG8_IRQHandler                     
0000115d  UART0_IRQHandler                     
0000115d  UART1_IRQHandler                     
0000115d  UART2_IRQHandler                     
0000115d  UART3_IRQHandler                     
00001161  Reset_Handler                        
00001165  _system_pre_init                     
00001168  C$$EXIT                              
00001169  abort                                
000011c0  __TI_Handler_Table_Base              
000011cc  __TI_Handler_Table_Limit             
000011e0  __TI_CINIT_Base                      
000011f0  __TI_CINIT_Limit                     
000011f0  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202001e0  Data_MotorPWM                        
202001e4  delayTick                            
202001e8  uwTick                               
202001ec  Flag_LED                             
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[106 symbols]
